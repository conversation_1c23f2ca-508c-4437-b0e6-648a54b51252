#!/bin/bash

# 代理配置测试脚本
# 用于测试Docker构建和运行时的代理配置

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 代理配置测试脚本 ===${NC}"

# 检查环境变量中的代理设置
echo -e "\n${YELLOW}1. 检查主机环境变量中的代理设置:${NC}"
echo "http_proxy: ${http_proxy:-未设置}"
echo "https_proxy: ${https_proxy:-未设置}"
echo "HTTP_PROXY: ${HTTP_PROXY:-未设置}"
echo "HTTPS_PROXY: ${HTTPS_PROXY:-未设置}"
echo "all_proxy: ${all_proxy:-未设置}"
echo "ALL_PROXY: ${ALL_PROXY:-未设置}"
echo "no_proxy: ${no_proxy:-未设置}"
echo "NO_PROXY: ${NO_PROXY:-未设置}"

# 检查.env文件中的代理设置
echo -e "\n${YELLOW}2. 检查.env文件中的代理设置:${NC}"
if [ -f ".env" ]; then
    echo "找到.env文件，代理相关配置："
    grep -E "^(http_proxy|https_proxy|HTTP_PROXY|HTTPS_PROXY|all_proxy|ALL_PROXY|no_proxy|NO_PROXY)=" .env || echo "未找到代理配置"
else
    echo -e "${RED}未找到.env文件${NC}"
fi

# 检查config.yaml中的代理设置
echo -e "\n${YELLOW}3. 检查config.yaml中的代理设置:${NC}"
if [ -f "config.yaml" ]; then
    echo "找到config.yaml文件，代理相关配置："
    grep -A 10 "^proxy:" config.yaml || echo "未找到代理配置"
else
    echo -e "${RED}未找到config.yaml文件${NC}"
fi

# 测试代理连接（如果设置了代理）
echo -e "\n${YELLOW}4. 测试代理连接:${NC}"
if [ ! -z "$http_proxy" ] || [ ! -z "$HTTP_PROXY" ]; then
    proxy_url="${http_proxy:-$HTTP_PROXY}"
    echo "测试HTTP代理连接: $proxy_url"
    
    if command -v curl &> /dev/null; then
        if curl -I --connect-timeout 10 --proxy "$proxy_url" http://www.google.com &> /dev/null; then
            echo -e "${GREEN}✓ HTTP代理连接正常${NC}"
        else
            echo -e "${RED}✗ HTTP代理连接失败${NC}"
        fi
    else
        echo "curl未安装，跳过代理连接测试"
    fi
else
    echo "未设置HTTP代理，跳过连接测试"
fi

# 检查Docker是否运行
echo -e "\n${YELLOW}5. 检查Docker环境:${NC}"
if command -v docker &> /dev/null; then
    if docker info &> /dev/null; then
        echo -e "${GREEN}✓ Docker正在运行${NC}"
        
        # 检查是否有运行中的backup-server容器
        if docker-compose ps backup-server | grep -q "Up"; then
            echo -e "${GREEN}✓ backup-server容器正在运行${NC}"
            
            # 检查容器内的代理环境变量
            echo -e "\n${YELLOW}6. 检查容器内的代理环境变量:${NC}"
            echo "容器内代理环境变量："
            docker-compose exec -T backup-server env | grep -E "(http_proxy|https_proxy|HTTP_PROXY|HTTPS_PROXY|all_proxy|ALL_PROXY|no_proxy|NO_PROXY)=" || echo "容器内未设置代理环境变量"
            
            # 检查应用日志中的代理配置
            echo -e "\n${YELLOW}7. 检查应用日志中的代理配置:${NC}"
            echo "应用启动时的代理配置："
            docker-compose logs backup-server | grep -E "(代理|proxy|Proxy)" | tail -10 || echo "未找到代理相关日志"
            
        else
            echo -e "${YELLOW}backup-server容器未运行，跳过容器内检查${NC}"
        fi
    else
        echo -e "${RED}✗ Docker未运行或无权限访问${NC}"
    fi
else
    echo -e "${RED}✗ Docker未安装${NC}"
fi

# 提供建议
echo -e "\n${BLUE}=== 配置建议 ===${NC}"
echo "1. 如果需要使用代理，推荐在环境变量中设置："
echo "   export http_proxy=http://proxy.company.com:8080"
echo "   export https_proxy=http://proxy.company.com:8080"
echo ""
echo "2. 或者在.env文件中设置："
echo "   http_proxy=http://proxy.company.com:8080"
echo "   https_proxy=http://proxy.company.com:8080"
echo ""
echo "3. 重新部署以应用代理配置："
echo "   ./scripts/quick_deploy.sh"
echo ""
echo "4. 查看详细的代理配置日志："
echo "   docker-compose logs backup-server | grep -i proxy"

echo -e "\n${GREEN}代理配置测试完成${NC}"
