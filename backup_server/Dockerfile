# Simple runtime container using pre-built binary
FROM alpine:latest

# Accept build arguments for target platform info (for logging)
ARG TARGETPLATFORM
ARG TARGETOS
ARG TARGETARCH

# Accept proxy build arguments
ARG http_proxy
ARG https_proxy
ARG no_proxy
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY

# Set proxy environment variables for package installation
ENV http_proxy=${http_proxy}
ENV https_proxy=${https_proxy}
ENV no_proxy=${no_proxy}
ENV HTTP_PROXY=${HTTP_PROXY}
ENV HTTPS_PROXY=${HTTPS_PROXY}
ENV NO_PROXY=${NO_PROXY}

# Install necessary packages for health checks and runtime
RUN apk add --no-cache wget ca-certificates tzdata

# Log build information
RUN echo "Building container for platform: ${TARGETPLATFORM:-unknown} (OS: ${TARGETOS:-linux}, ARCH: ${TARGETARCH:-unknown})"

WORKDIR /app

# Copy the pre-built binary (built by the deployment script)
COPY backup-server /app/backup-server

# Ensure the binary is executable
RUN chmod +x /app/backup-server

# Copy configuration file
COPY config.yaml /app/

# Copy web assets
COPY web/ /app/web/

# Copy scripts
COPY scripts/ /app/scripts/

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV TZ=UTC

# Clear proxy environment variables for runtime (they should come from docker-compose)
ENV http_proxy=
ENV https_proxy=
ENV no_proxy=
ENV HTTP_PROXY=
ENV HTTPS_PROXY=
ENV NO_PROXY=

# Expose backup server port
EXPOSE 8080

# Health check (using wget which is available in Alpine)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/storage/stats || exit 1

# Use absolute path for the binary
CMD ["/app/backup-server"]
