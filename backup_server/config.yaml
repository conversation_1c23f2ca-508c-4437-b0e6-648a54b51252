# 备份服务器配置文件

server:
  host: "0.0.0.0"
  port: "8080"

# 后端服务配置
backend:
  url: "https://api.caby.care"  # 生产环境API地址
  auth_token: "FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# MinIO配置（将从后端API获取，这里作为备用）
minio:
  endpoint: "***************:9000"
  access_key: "animsuper"
  secret_key: "4vbtCeEQgcN2uB"
  use_ssl: false
  bucket: "records"

# 存储配置
storage:
  data_path: "./data"  # 本地数据存储路径

# 下载配置
download:
  max_concurrent: 5      # 最大并发下载数
  chunk_size: 1048576    # 下载块大小 (1MB)

# 代理配置
# 注意：环境变量中的代理设置优先级高于配置文件
# 如果环境变量中设置了代理，将使用环境变量的值
# 如果需要使用代理，请设置以下值或在环境变量中设置
# 例如: http_proxy: "http://proxy.company.com:8080"
# 例如: https_proxy: "http://proxy.company.com:8080"
# 例如: all_proxy: "socks5://proxy.company.com:1080"
proxy:
  http_proxy: ""   # HTTP代理地址
  https_proxy: ""  # HTTPS代理地址
  all_proxy: ""    # 通用代理地址（支持HTTP/HTTPS/SOCKS5）
